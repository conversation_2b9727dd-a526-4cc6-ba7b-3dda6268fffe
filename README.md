# BESS Simulation Project

## Overview
This project simulates Battery Energy Storage System (BESS) operation for renewable energy integration. The Streamlit-based application allows users to:

- Simulate BESS charging/discharging behavior
- Visualize generation schedules and actual outputs
- Test automatic schedule revision logic
- Analyze system performance metrics

## Key Features
- Dynamic schedule revision system
- Real-time simulation visualization
- Configurable BESS parameters
- Test data generation utility

## Project Structure
- `main.py`: Main Streamlit application
- `create_test_data.py`: Test data generator
- `test_data.xlsx`: Sample test data

[//]: # (Additional documentation sections will be linked here)