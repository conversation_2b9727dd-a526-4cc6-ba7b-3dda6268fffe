import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import time
import io

# --- Constants ---
TIME_BLOCK_DURATION_HOURS = 15 / 60  # 15 minutes in hours
TOTAL_TIME_BLOCKS = 96
DEVIATION_TOLERANCE_PERCENT = 10

# --- Schedule Revision Constants ---
REVISION_TRIGGER_INTERVAL = 2  # Every 2 time blocks (30 minutes)
ODD_BLOCK_EFFECTIVE_DELAY = 7  # Effective from 7th block onwards for odd block revisions
EVEN_BLOCK_EFFECTIVE_DELAY = 8  # Effective from 8th block onwards for even block revisions

# --- Schedule Revision Helper Functions ---
def should_trigger_revision(time_block_numeric):
    """
    Determines if a schedule revision should be triggered at the current time block.
    Revisions are triggered every 30 minutes (2 time blocks).
    """
    return time_block_numeric % REVISION_TRIGGER_INTERVAL == 0

def calculate_effective_time_block(revision_time_block):
    """
    Calculates the effective time block when a revision becomes active.

    Args:
        revision_time_block (int): The time block when revision is submitted

    Returns:
        int: The time block when the revision becomes effective
    """
    if revision_time_block % 2 == 1:  # Odd time block
        return revision_time_block + ODD_BLOCK_EFFECTIVE_DELAY - 1
    else:  # Even time block
        return revision_time_block + EVEN_BLOCK_EFFECTIVE_DELAY - 1



# --- Helper Function to Simulate BESS Operation ---
def simulate_bess(
    generation_df,
    bess_capacity_mwh,
    bess_power_rating_mw,
    bess_soh_percent,
    bess_self_discharge_rate_percent_per_day,
    initial_soc_percent,
    bess_round_trip_efficiency_percent,
    min_soc_percent, # New input parameter
    max_soc_percent, # New input parameter
    chart_placeholder,
    status_placeholder
):
    """
    Simulates the BESS operation block by block and updates a live chart.
    """
    # Initialize BESS state
    max_capacity_kwh = bess_capacity_mwh * 1000
    
    # Calculate min/max SoC in kWh
    min_soc_kwh = (min_soc_percent / 100) * max_capacity_kwh
    max_soc_kwh = (max_soc_percent / 100) * max_capacity_kwh

    # Ensure initial SoC is within the new min/max bounds
    initial_soc_kwh = (initial_soc_percent / 100) * max_capacity_kwh
    current_soc_kwh = max(min_soc_kwh, min(initial_soc_kwh, max_soc_kwh))


    # Convert round-trip efficiency to a factor for calculations
    efficiency_factor = bess_round_trip_efficiency_percent / 100.0
    if efficiency_factor == 0: # Avoid division by zero if efficiency is 0
        st.error("Round-Trip Efficiency cannot be 0%. Please set a value greater than 0.")
        return


    # Lists to store data for plotting
    time_blocks_display = [] # For HH:MM format on x-axis
    time_blocks_numeric = [] # For iteration
    actual_gen_data = []
    scheduled_gen_data = []
    avc_data = []
    upper_bound_data = []
    lower_bound_data = []
    bess_action_data = []  # Positive for charging, negative for discharging (MW at grid connection)
    actual_gen_with_bess_data = [] # New list for adjusted actual generation
    bess_soc_percent_data = []

    # New lists for exceeding bands data
    mw_exceeding_before_bess_data = []
    mw_exceeding_after_bess_data = []

    # Schedule revision data structures
    original_scheduled_gen = list(generation_df["Scheduled Generation"])  # Store original schedule
    revised_scheduled_gen = list(generation_df["Scheduled Generation"])   # Working copy for revisions
    effective_scheduled_gen_data = []  # For plotting effective schedule
    revision_history = []  # Track all revisions: [(time_block, effective_time_block, avc_value)]
    revision_markers = []  # For chart annotations
    current_effective_time_block = None  # Track when current revision becomes effective

    total_loss_mwh = 0
    total_deviation_mwh_before_bess = 0 # Cumulative energy deviation outside band before BESS action (MWh)
    
    sum_mw_exceeding_before_bess = 0 # Cumulative instantaneous MW deviation before BESS action (MW)
    sum_mw_exceeding_after_bess = 0  # Cumulative instantaneous MW deviation after BESS action (MW)

    status_placeholder.info("Starting simulation...")

    for i in range(min(TOTAL_TIME_BLOCKS, len(generation_df))):
        time_block_numeric = i + 1

        # Get generation data for the current time block
        try:
            # Assuming the dataframe is already sorted by Time Block.
            row = generation_df.iloc[i]
            time_block_str = str(row["Time Block"]) # Convert to string to handle time objects
            actual_gen_mw = float(row["Actual Generation"])
            scheduled_gen_mw = float(row["Scheduled Generation"])
            avc_mw = float(row["AvC"])

        except IndexError:
            status_placeholder.error(f"Data missing for Time Block {time_block_numeric}. Stopping simulation.")
            break
        except KeyError as e:
            status_placeholder.error(f"Missing expected column in uploaded file: {e}. Please check your file format.")
            break
        except (ValueError, TypeError) as e:
            status_placeholder.error(f"Error converting data to numeric values at Time Block {time_block_numeric}: {e}")
            break

        # --- Schedule Revision Logic ---
        if should_trigger_revision(time_block_numeric):
            # Calculate effective time block for this revision
            effective_time_block = calculate_effective_time_block(time_block_numeric)

            # Only apply revision if effective time is within simulation range
            if effective_time_block <= TOTAL_TIME_BLOCKS and effective_time_block > 0:
                # Update all future scheduled generation from effective time onwards
                # Ensure we don't go beyond the available data
                max_index = min(TOTAL_TIME_BLOCKS, len(revised_scheduled_gen))
                start_index = max(0, effective_time_block - 1)

                for future_block in range(start_index, max_index):
                    if future_block < len(revised_scheduled_gen):
                        revised_scheduled_gen[future_block] = avc_mw

                # Record this revision
                revision_history.append((time_block_numeric, effective_time_block, avc_mw))
                revision_markers.append({
                    'time_block': time_block_str,
                    'revision_block': time_block_numeric,
                    'effective_block': effective_time_block,
                    'avc_value': avc_mw
                })
                current_effective_time_block = effective_time_block

        # Determine effective scheduled generation for current block
        if current_effective_time_block and time_block_numeric >= current_effective_time_block:
            effective_scheduled_gen_mw = revised_scheduled_gen[i]
        else:
            effective_scheduled_gen_mw = original_scheduled_gen[i]

        # Use effective scheduled generation for calculations
        scheduled_gen_mw = effective_scheduled_gen_mw

        # Calculate deviation and bounds based on AvC
        tolerance_value_mw = avc_mw * (DEVIATION_TOLERANCE_PERCENT / 100)
        upper_bound_mw = scheduled_gen_mw + tolerance_value_mw
        lower_bound_mw = scheduled_gen_mw - tolerance_value_mw
        
        bess_action_mw = 0 # Power exchanged with the grid (positive for charging, negative for discharging)
        block_loss_mwh = 0
        
        current_mw_exceeding_before_bess = 0 # MW exceeding band before BESS action for this block
        current_mw_exceeding_after_bess = 0 # MW exceeding band after BESS action for this block

        # Apply SoH to effective power rating
        effective_power_rating_mw = bess_power_rating_mw * (bess_soh_percent / 100)

        # --- Calculate MW exceeding before BESS ---
        if actual_gen_mw > upper_bound_mw:
            current_mw_exceeding_before_bess = actual_gen_mw - upper_bound_mw
            total_deviation_mwh_before_bess += current_mw_exceeding_before_bess * TIME_BLOCK_DURATION_HOURS / 1000
        elif actual_gen_mw < lower_bound_mw:
            current_mw_exceeding_before_bess = lower_bound_mw - actual_gen_mw # Absolute value of under-injection
            total_deviation_mwh_before_bess += current_mw_exceeding_before_bess * TIME_BLOCK_DURATION_HOURS / 1000
        
        sum_mw_exceeding_before_bess += current_mw_exceeding_before_bess

        # --- Handle Over-injection (Charging BESS) ---
        # BESS charges to bring actual_gen_mw down to upper_bound_mw
        if actual_gen_mw > upper_bound_mw:
            over_injection_to_handle_mw = actual_gen_mw - upper_bound_mw
            
            # Max charge rate limited by BESS power rating
            charge_rate_limit_by_power_mw = effective_power_rating_mw
            
            # Max charge rate limited by max SoC
            # How much power can be taken from grid to reach max_soc_kwh, considering efficiency?
            charge_capacity_available_kwh = max(0.0, max_soc_kwh - current_soc_kwh) # Ensure non-negative
            charge_rate_limit_by_soc_mw = charge_capacity_available_kwh / (efficiency_factor * TIME_BLOCK_DURATION_HOURS) if efficiency_factor > 0 and TIME_BLOCK_DURATION_HOURS > 0 else 0.0 # Set to 0 if no capacity or efficiency issue
            
            charge_amount_mw = min(over_injection_to_handle_mw, charge_rate_limit_by_power_mw, charge_rate_limit_by_soc_mw)
            
            # Update SoC: energy stored in battery accounts for efficiency
            current_soc_kwh += charge_amount_mw * TIME_BLOCK_DURATION_HOURS * efficiency_factor
            bess_action_mw = charge_amount_mw # This is the power absorbed from the grid

            # Calculate loss (energy that couldn't be absorbed by BESS)
            block_loss_mwh = (over_injection_to_handle_mw - charge_amount_mw) * TIME_BLOCK_DURATION_HOURS / 1000 # Convert kWh to MWh
            current_mw_exceeding_after_bess = over_injection_to_handle_mw - charge_amount_mw # Remaining MW deviation

        # --- Handle Under-injection (Discharging BESS) ---
        # BESS discharges to bring actual_gen_mw up to lower_bound_mw
        elif actual_gen_mw < lower_bound_mw:
            under_injection_to_handle_mw = lower_bound_mw - actual_gen_mw
            
            # Max discharge rate limited by BESS power rating
            discharge_rate_limit_by_power_mw = effective_power_rating_mw
            
            # Max discharge rate limited by min SoC
            # How much power can be delivered to grid from current_soc_kwh to min_soc_kwh, considering efficiency?
            discharge_capacity_available_kwh = max(0.0, current_soc_kwh - min_soc_kwh) # Ensure non-negative
            discharge_rate_limit_by_soc_mw = (discharge_capacity_available_kwh * efficiency_factor) / TIME_BLOCK_DURATION_HOURS if TIME_BLOCK_DURATION_HOURS > 0 else 0.0 # Set to 0 if no capacity or efficiency issue
            
            discharge_amount_mw = min(under_injection_to_handle_mw, discharge_rate_limit_by_power_mw, discharge_rate_limit_by_soc_mw)
            
            # Update SoC: energy consumed from battery accounts for efficiency
            current_soc_kwh -= (discharge_amount_mw * TIME_BLOCK_DURATION_HOURS) / efficiency_factor 
            bess_action_mw = -discharge_amount_mw # This is the power supplied to the grid (negative)

            # Calculate loss (energy that couldn't be supplied by BESS)
            block_loss_mwh = (under_injection_to_handle_mw - discharge_amount_mw) * TIME_BLOCK_DURATION_HOURS / 1000 # Convert kWh to MWh
            current_mw_exceeding_after_bess = under_injection_to_handle_mw - discharge_amount_mw # Remaining MW deviation
        
        sum_mw_exceeding_after_bess += current_mw_exceeding_after_bess

        # Apply self-discharge for the current block
        self_discharge_kwh_per_block = (max_capacity_kwh * (bess_self_discharge_rate_percent_per_day / 100)) / TOTAL_TIME_BLOCKS
        current_soc_kwh = max(0, current_soc_kwh - self_discharge_kwh_per_block)

        # Ensure SoC stays within the defined min/max bounds (and overall 0-max_capacity_kwh)
        # This final clamping is crucial to prevent any floating point inaccuracies from pushing SoC slightly out of bounds
        current_soc_kwh = max(min_soc_kwh, min(current_soc_kwh, max_soc_kwh))
        current_soc_kwh = max(0, min(current_soc_kwh, max_capacity_kwh)) # Final clamp to absolute min/max

        # Calculate current SoC percentage
        current_soc_percent = (current_soc_kwh / max_capacity_kwh) * 100 if max_capacity_kwh > 0 else 0

        # Calculate Actual Generation including BESS action
        # If BESS is charging (bess_action_mw > 0), it's absorbing power, so subtract from actual_gen_mw
        # If BESS is discharging (bess_action_mw < 0), it's adding power, so subtract a negative (add) to actual_gen_mw
        actual_gen_with_bess_mw = actual_gen_mw - bess_action_mw 

        # Accumulate total loss
        total_loss_mwh += block_loss_mwh

        # Store data for plotting
        time_blocks_display.append(time_block_str)
        time_blocks_numeric.append(time_block_numeric)
        actual_gen_data.append(actual_gen_mw)
        scheduled_gen_data.append(original_scheduled_gen[i])  # Store original schedule
        effective_scheduled_gen_data.append(effective_scheduled_gen_mw)  # Store effective schedule
        avc_data.append(avc_mw)
        upper_bound_data.append(upper_bound_mw)
        lower_bound_data.append(lower_bound_mw)
        bess_action_data.append(bess_action_mw)
        actual_gen_with_bess_data.append(actual_gen_with_bess_mw) # Store adjusted actual generation
        bess_soc_percent_data.append(current_soc_percent)

        mw_exceeding_before_bess_data.append(current_mw_exceeding_before_bess)
        mw_exceeding_after_bess_data.append(current_mw_exceeding_after_bess)


        # Update the live chart
        df_plot = pd.DataFrame({
            "Time Block Display": time_blocks_display,
            "Time Block Numeric": time_blocks_numeric,
            "Actual Generation (MW)": actual_gen_data,
            "Scheduled Generation (MW)": scheduled_gen_data,
            "Effective Scheduled Generation (MW)": effective_scheduled_gen_data,
            "AvC (MW)": avc_data,
            "Upper Bound (MW)": upper_bound_data,
            "Lower Bound (MW)": lower_bound_data,
            "BESS Action (MW)": bess_action_data,
            "Actual Generation with BESS (MW)": actual_gen_with_bess_data, # Add to DataFrame
            "BESS SoC (%)": bess_soc_percent_data
        })

        fig = go.Figure()
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["Actual Generation (MW)"], mode='lines', name='Original Actual Generation (MW)', line=dict(color='blue')))
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["Actual Generation with BESS (MW)"], mode='lines', name='Actual Generation with BESS (MW)', line=dict(color='darkgreen', width=3))) # New line
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["Scheduled Generation (MW)"], mode='lines', name='Original Scheduled Generation (MW)', line=dict(color='lightgreen', dash='dot')))
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["Effective Scheduled Generation (MW)"], mode='lines', name='Effective Scheduled Generation (MW)', line=dict(color='green', dash='dot', width=2)))
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["Upper Bound (MW)"], mode='lines', name='Upper Bound (MW)', line=dict(color='orange', dash='dash')))
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["Lower Bound (MW)"], mode='lines', name='Lower Bound (MW)', line=dict(color='red', dash='dash')))
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["BESS Action (MW)"], mode='lines', name='BESS Charge/Discharge (MW)', yaxis='y2', line=dict(color='purple')))
        fig.add_trace(go.Scatter(x=df_plot["Time Block Display"], y=df_plot["BESS SoC (%)"], mode='lines', name='BESS SoC (%)', yaxis='y3', line=dict(color='brown')))

        # Add revision markers
        for marker in revision_markers:
            if marker['time_block'] in time_blocks_display:
                fig.add_vline(
                    x=marker['time_block'],
                    line_dash="dash",
                    line_color="red",
                    annotation_text=f"Revision at {marker['time_block']}<br>Effective: Block {marker['effective_block']}<br>AvC: {marker['avc_value']:.2f} MW",
                    annotation_position="top"
                )

        fig.update_layout(
            title=f"BESS Simulation - Time Block {time_block_str} ({time_block_numeric}/{TOTAL_TIME_BLOCKS})",
            xaxis_title="Time Block",
            yaxis=dict(title="Generation (MW)", side='left'),
            yaxis2=dict(title="BESS Action (MW)", overlaying='y', side='right', showgrid=False, range=[df_plot["BESS Action (MW)"].min() * 1.1, df_plot["BESS Action (MW)"].max() * 1.1]), # Dynamic range for BESS action
            yaxis3=dict(title="BESS SoC (%)", overlaying='y', side='right', position=0.95, showgrid=False, range=[0, 100]),
            hovermode="x unified",
            height=600,
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )
        chart_placeholder.plotly_chart(fig, use_container_width=True)

        # Build status message with schedule revision information
        status_message = (
            f"**Time Block: {time_block_str} ({time_block_numeric}/{TOTAL_TIME_BLOCKS})**\n"
            f"Original Actual Gen: {actual_gen_mw:.2f} MW\n"
            f"Actual Gen with BESS: {actual_gen_with_bess_mw:.2f} MW\n"
            f"Original Scheduled Gen: {original_scheduled_gen[i]:.2f} MW\n"
            f"Effective Scheduled Gen: {effective_scheduled_gen_mw:.2f} MW\n"
            f"AvC: {avc_mw:.2f} MW\n"
            f"Upper Bound: {upper_bound_mw:.2f} MW, Lower Bound: {lower_bound_mw:.2f} MW\n"
            f"BESS Action: {bess_action_mw:.2f} MW (Positive = Charge, Negative = Discharge)\n"
            f"Current SoC: {current_soc_percent:.2f} %\n"
            f"MW Exceeding Band (Before BESS): {current_mw_exceeding_before_bess:.2f} MW\n"
            f"MW Exceeding Band (After BESS): {current_mw_exceeding_after_bess:.2f} MW\n"
            f"Loss in this block (MWh): {block_loss_mwh:.4f}\n"
        )

        # Add revision information if triggered
        if should_trigger_revision(time_block_numeric):
            effective_time_block = calculate_effective_time_block(time_block_numeric)
            if effective_time_block <= TOTAL_TIME_BLOCKS:
                revision_info = (
                    f"\n🔄 **SCHEDULE REVISION TRIGGERED!**\n"
                    f"Revision submitted at Block {time_block_numeric} ({'Odd' if time_block_numeric % 2 == 1 else 'Even'})\n"
                    f"Effective from Block {effective_time_block} onwards\n"
                    f"New scheduled generation: {avc_mw:.2f} MW (based on current AvC)"
                )
                status_message = status_message + revision_info

        # Add information about active revisions
        if current_effective_time_block and time_block_numeric >= current_effective_time_block:
            active_revision_info = f"\n📋 Active revision in effect (since Block {current_effective_time_block})"
            status_message = status_message + active_revision_info

        status_placeholder.info(status_message)
        time.sleep(0.5) # Delay for smoother updates

    # Build final status message with revision summary
    final_status = (
        f"Simulation complete!\n\n"
        f"**Total MW Exceeding Confidence Band (Before BESS): {sum_mw_exceeding_before_bess:.2f} MW** (Sum of instantaneous deviations)\n"
        f"**Total MW Exceeding Confidence Band (After BESS): {sum_mw_exceeding_after_bess:.2f} MW** (Sum of instantaneous deviations)\n"
        f"Total Energy Deviation Before BESS Intervention (MWh): {total_deviation_mwh_before_bess:.4f}\n"
        f"Total Energy Loss After BESS Intervention (MWh): {total_loss_mwh:.4f}\n\n"
    )

    # Add schedule revision summary
    if revision_history:
        final_status += f"**Schedule Revisions Summary:**\n"
        final_status += f"Total revisions triggered: {len(revision_history)}\n"
        for i, (rev_block, eff_block, avc_val) in enumerate(revision_history, 1):
            final_status += f"  {i}. Block {rev_block} → Effective Block {eff_block} (AvC: {avc_val:.2f} MW)\n"
    else:
        final_status += "**No schedule revisions were triggered during this simulation.**"

    status_placeholder.success(final_status)

# --- Streamlit App Layout ---
st.set_page_config(layout="wide", page_title="BESS Operation Simulation")

st.title("🔋 BESS Operation Simulation")

st.markdown(
    """
    This application simulates the charging and discharging of a Battery Energy Storage System (BESS)
    based on actual and scheduled generation data. The BESS intervenes when the actual generation
    deviates by more than 10% of the **AvC (Available Capacity)** from the scheduled generation,
    aiming to keep the generation within the defined confidence band.

    **Schedule Revision Feature:**
    - Every 30 minutes (2 time blocks), a schedule revision is automatically triggered
    - For odd time blocks: revision becomes effective from the 7th time block onwards
    - For even time blocks: revision becomes effective from the 8th time block onwards
    - When triggered, all future scheduled generation is updated to the current AvC value
    - The chart displays both original and dynamically updated effective scheduled generation
    """
    """
    **Note on C-Rate:** The C-Rate is implicitly determined by the ratio of the BESS Power Rating (MW)
    to its Capacity (MWh). For example, a 10 MW BESS with a 10 MWh capacity would operate at a 1C rate.
    """
)

# --- Sidebar for Inputs ---
st.sidebar.header("BESS Parameters")

bess_capacity_mwh = st.sidebar.number_input(
    "BESS Capacity (MWh)",
    min_value=1.0,
    max_value=1000.0,
    value=10.0,
    step=1.0,
    help="Total energy storage capacity of the BESS in MWh."
)
bess_power_rating_mw = st.sidebar.number_input(
    "BESS Power Rating (MW)",
    min_value=0.1,
    max_value=500.0,
    value=5.0,
    step=0.1,
    help="Maximum instantaneous power the BESS can charge or discharge in MW."
)
bess_soh_percent = st.sidebar.slider(
    "BESS State of Health (SoH) (%)",
    min_value=0,
    max_value=100,
    value=90,
    step=1,
    help="Current health of the battery as a percentage (100% = new, lower = degraded capacity/efficiency)."
)
bess_self_discharge_rate_percent_per_day = st.sidebar.number_input(
    "Self-Discharge Rate (% per day)",
    min_value=0.0,
    max_value=5.0,
    value=0.1,
    step=0.01,
    help="Percentage of stored energy lost per day due to internal reactions."
)
initial_soc_percent = st.sidebar.slider(
    "Initial State of Charge (SoC) (%)",
    min_value=0,
    max_value=100,
    value=50,
    step=1,
    help="Initial charge level of the BESS at the start of the simulation."
)
min_soc_percent = st.sidebar.slider(
    "Minimum SoC (%)",
    min_value=0,
    max_value=100,
    value=20, # Default minimum SoC
    step=1,
    help="The minimum State of Charge the BESS is allowed to reach before it stops discharging."
)
max_soc_percent = st.sidebar.slider(
    "Maximum SoC (%)",
    min_value=0,
    max_value=100,
    value=80, # Default maximum SoC
    step=1,
    help="The maximum State of Charge the BESS is allowed to reach before it stops charging."
)
# Ensure min_soc is not greater than max_soc
if min_soc_percent >= max_soc_percent:
    st.sidebar.warning("Minimum SoC cannot be greater than or equal to Maximum SoC. Adjusting Minimum SoC.")
    min_soc_percent = max_soc_percent - 1 if max_soc_percent > 0 else 0


bess_round_trip_efficiency_percent = st.sidebar.slider(
    "Round-Trip Efficiency (%)",
    min_value=0,
    max_value=100,
    value=90,
    step=1,
    help="Overall efficiency of a full charge and discharge cycle. Accounts for energy losses during storage and retrieval."
)

st.sidebar.markdown("---")
st.sidebar.header("Upload Generation Data (Excel)")

uploaded_generation_file = st.sidebar.file_uploader(
    "Upload Generation Data (Excel)",
    type=["xlsx", "xls"],
    help="Excel file with 'Time Block' (HH:MM), 'Scheduled Generation' (MW), 'Actual Generation' (MW), and 'AvC' (MW) columns."
)

# --- Main Content Area ---
st.header("Simulation Results")

if uploaded_generation_file:
    try:
        generation_df = pd.read_excel(uploaded_generation_file)

        # Validate columns
        required_columns = ["Time Block", "Scheduled Generation", "Actual Generation", "AvC"]
        if not all(col in generation_df.columns for col in required_columns):
            st.error(f"Uploaded file must contain columns: {required_columns}")
        else:
            # Display dataframe preview
            st.subheader("Uploaded Data Preview")
            st.dataframe(generation_df.head())

            st.markdown("---")
            st.subheader("Live Simulation")

            # Placeholders for live updates
            status_message_placeholder = st.empty()
            chart_placeholder = st.empty()

            if st.button("Start Simulation"):
                simulate_bess(
                    generation_df,
                    bess_capacity_mwh,
                    bess_power_rating_mw,
                    bess_soh_percent,
                    bess_self_discharge_rate_percent_per_day,
                    initial_soc_percent,
                    bess_round_trip_efficiency_percent,
                    min_soc_percent, # Pass the new parameter
                    max_soc_percent, # Pass the new parameter
                    chart_placeholder,
                    status_message_placeholder
                )
            else:
                status_message_placeholder.info("Click 'Start Simulation' to begin.")

    except Exception as e:
        st.error(f"Error reading file or processing data: {e}")
        st.info("Please ensure your Excel file is correctly formatted with the specified columns.")
else:
    st.info("Please upload the 'Generation Data' Excel file to start the simulation.")

st.markdown("---")
 