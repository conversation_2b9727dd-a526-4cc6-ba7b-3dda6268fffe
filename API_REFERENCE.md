# BESS Simulation API Reference

## Core Functions

### `simulate_bess(generation_df, bess_capacity_mwh, bess_power_rating_mw, bess_soh_percent, bess_self_discharge_rate_percent_per_day, initial_soc_percent, bess_round_trip_efficiency_percent, min_soc_percent, max_soc_percent, chart_placeholder, status_placeholder)`

Main simulation function that models BESS operation.

**Parameters:**
- `generation_df`: DataFrame containing time blocks, scheduled/actual generation, and AvC values
- `bess_capacity_mwh`: Total energy storage capacity in MWh
- `bess_power_rating_mw`: Maximum charge/discharge power in MW
- `bess_soh_percent`: Battery state of health percentage
- `bess_self_discharge_rate_percent_per_day`: Daily self-discharge rate
- `initial_soc_percent`: Starting state of charge
- `bess_round_trip_efficiency_percent`: Round-trip efficiency percentage
- `min_soc_percent`: Minimum allowed state of charge
- `max_soc_percent`: Maximum allowed state of charge
- `chart_placeholder`: Streamlit placeholder for the chart
- `status_placeholder`: Streamlit placeholder for status messages

**Behavior:**
- Simulates BESS operation in 15-minute time blocks
- Automatically triggers schedule revisions every 30 minutes
- Maintains battery state of charge within configured limits
- Visualizes results in real-time using Plotly charts
