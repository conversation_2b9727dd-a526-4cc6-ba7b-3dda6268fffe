#!/usr/bin/env python3
"""
Test script for schedule revision functionality
"""

# Test the helper functions
def should_trigger_revision(time_block_numeric):
    """
    Determines if a schedule revision should be triggered at the current time block.
    Revisions are triggered every 30 minutes (2 time blocks).
    """
    REVISION_TRIGGER_INTERVAL = 2
    return time_block_numeric % REVISION_TRIGGER_INTERVAL == 0

def calculate_effective_time_block(revision_time_block):
    """
    Calculates the effective time block when a revision becomes active.
    
    Args:
        revision_time_block (int): The time block when revision is submitted
        
    Returns:
        int: The time block when the revision becomes effective
    """
    ODD_BLOCK_EFFECTIVE_DELAY = 7
    EVEN_BLOCK_EFFECTIVE_DELAY = 8
    
    if revision_time_block % 2 == 1:  # Odd time block
        return revision_time_block + ODD_BLOCK_EFFECTIVE_DELAY - 1
    else:  # Even time block
        return revision_time_block + EVEN_BLOCK_EFFECTIVE_DELAY - 1

# Test the functions
print("Testing Schedule Revision Logic:")
print("=" * 40)

for time_block in range(1, 21):
    if should_trigger_revision(time_block):
        effective_block = calculate_effective_time_block(time_block)
        block_type = "Odd" if time_block % 2 == 1 else "Even"
        print(f"Block {time_block:2d} ({block_type:4s}): Revision triggered → Effective from Block {effective_block}")

print("\nTesting with sample data:")
print("=" * 40)

import pandas as pd

# Load the test data
try:
    df = pd.read_excel('test_data.xlsx')
    print(f"Loaded {len(df)} rows of data")
    print("\nFirst 5 rows:")
    print(df.head())
    
    print(f"\nData types:")
    print(df.dtypes)
    
    # Test processing the first few rows
    print(f"\nTesting data processing:")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        time_block_str = str(row["Time Block"])
        actual_gen_mw = float(row["Actual Generation"])
        scheduled_gen_mw = float(row["Scheduled Generation"])
        avc_mw = float(row["AvC"])
        
        print(f"Row {i+1}: Time={time_block_str}, Actual={actual_gen_mw:.2f}, Scheduled={scheduled_gen_mw:.2f}, AvC={avc_mw:.2f}")
        
except Exception as e:
    print(f"Error loading test data: {e}")
