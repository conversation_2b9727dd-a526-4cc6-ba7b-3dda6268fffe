#!/usr/bin/env python3
"""
Test the BESS simulation with schedule revision functionality
"""

import pandas as pd
import numpy as np

# Constants
TIME_BLOCK_DURATION_HOURS = 15 / 60  # 15 minutes in hours
TOTAL_TIME_BLOCKS = 96
DEVIATION_TOLERANCE_PERCENT = 10

# Schedule Revision Constants
REVISION_TRIGGER_INTERVAL = 2  # Every 2 time blocks (30 minutes)
ODD_BLOCK_EFFECTIVE_DELAY = 7  # Effective from 7th block onwards for odd block revisions
EVEN_BLOCK_EFFECTIVE_DELAY = 8  # Effective from 8th block onwards for even block revisions

# Schedule Revision Helper Functions
def should_trigger_revision(time_block_numeric):
    return time_block_numeric % REVISION_TRIGGER_INTERVAL == 0

def calculate_effective_time_block(revision_time_block):
    if revision_time_block % 2 == 1:  # Odd time block
        return revision_time_block + ODD_BLOCK_EFFECTIVE_DELAY - 1
    else:  # Even time block
        return revision_time_block + EVEN_BLOCK_EFFECTIVE_DELAY - 1

# Load test data
print("Loading test data...")
generation_df = pd.read_excel('test_data.xlsx')
print(f"Loaded {len(generation_df)} rows")

# Initialize schedule revision data structures
original_scheduled_gen = list(generation_df["Scheduled Generation"])
revised_scheduled_gen = list(generation_df["Scheduled Generation"])
revision_history = []
current_effective_time_block = None

print("\nTesting simulation loop...")

# Test first 10 time blocks
for i in range(min(10, len(generation_df))):
    time_block_numeric = i + 1
    
    try:
        # Get generation data for the current time block
        row = generation_df.iloc[i]
        time_block_str = str(row["Time Block"])
        actual_gen_mw = float(row["Actual Generation"])
        scheduled_gen_mw = float(row["Scheduled Generation"])
        avc_mw = float(row["AvC"])

        print(f"\nTime Block {time_block_numeric} ({time_block_str}):")
        print(f"  Original data: Actual={actual_gen_mw:.2f}, Scheduled={scheduled_gen_mw:.2f}, AvC={avc_mw:.2f}")

        # Schedule Revision Logic
        if should_trigger_revision(time_block_numeric):
            effective_time_block = calculate_effective_time_block(time_block_numeric)
            print(f"  🔄 REVISION TRIGGERED! Effective from block {effective_time_block}")
            
            if effective_time_block <= len(generation_df):
                # Update future scheduled generation
                max_index = min(len(generation_df), len(revised_scheduled_gen))
                start_index = max(0, effective_time_block - 1)
                
                for future_block in range(start_index, max_index):
                    if future_block < len(revised_scheduled_gen):
                        revised_scheduled_gen[future_block] = avc_mw
                
                revision_history.append((time_block_numeric, effective_time_block, avc_mw))
                current_effective_time_block = effective_time_block

        # Determine effective scheduled generation
        if current_effective_time_block and time_block_numeric >= current_effective_time_block:
            effective_scheduled_gen_mw = revised_scheduled_gen[i]
            print(f"  📋 Using revised schedule: {effective_scheduled_gen_mw:.2f} MW")
        else:
            effective_scheduled_gen_mw = original_scheduled_gen[i]
            print(f"  📋 Using original schedule: {effective_scheduled_gen_mw:.2f} MW")

        # Calculate bounds
        tolerance_value_mw = avc_mw * (DEVIATION_TOLERANCE_PERCENT / 100)
        upper_bound_mw = effective_scheduled_gen_mw + tolerance_value_mw
        lower_bound_mw = effective_scheduled_gen_mw - tolerance_value_mw
        
        print(f"  Bounds: [{lower_bound_mw:.2f}, {upper_bound_mw:.2f}] MW")
        
        # Test status message formatting
        status_message = (
            f"**Time Block: {str(time_block_str)} ({int(time_block_numeric)}/{int(len(generation_df))})**\n"
            f"Original Actual Gen: {float(actual_gen_mw):.2f} MW\n"
            f"Original Scheduled Gen: {float(original_scheduled_gen[i]):.2f} MW\n"
            f"Effective Scheduled Gen: {float(effective_scheduled_gen_mw):.2f} MW\n"
            f"AvC: {float(avc_mw):.2f} MW"
        )
        
        print(f"  Status message created successfully")

    except Exception as e:
        print(f"  ❌ Error at time block {time_block_numeric}: {e}")
        break

print(f"\nRevision History: {revision_history}")
print("Test completed successfully!")
