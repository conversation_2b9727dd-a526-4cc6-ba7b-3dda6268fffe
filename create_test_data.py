import pandas as pd
import numpy as np

# Create test data for schedule revision testing
time_blocks = []
scheduled_gen = []
actual_gen = []
avc = []

# Generate 20 time blocks for testing (about 5 hours)
for i in range(1, 21):
    # Time block in HH:MM format (starting from 00:00, 15-minute intervals)
    hours = (i - 1) * 15 // 60
    minutes = (i - 1) * 15 % 60
    time_block = f"{hours:02d}:{minutes:02d}"
    time_blocks.append(time_block)
    
    # Base scheduled generation with some variation
    base_scheduled = 50 + 10 * np.sin(i * 0.3)
    scheduled_gen.append(base_scheduled)
    
    # Actual generation with some deviation
    actual_gen.append(base_scheduled + np.random.normal(0, 5))
    
    # AvC with different values to test revision logic
    if i <= 5:
        avc.append(60)  # Higher AvC initially
    elif i <= 10:
        avc.append(45)  # Lower AvC in middle
    else:
        avc.append(55)  # Different AvC later

# Create DataFrame
df = pd.DataFrame({
    'Time Block': time_blocks,
    'Scheduled Generation': scheduled_gen,
    'Actual Generation': actual_gen,
    'AvC': avc
})

# Save to Excel
df.to_excel('test_data.xlsx', index=False)
print("Test data created successfully!")
print(df.head(10))
